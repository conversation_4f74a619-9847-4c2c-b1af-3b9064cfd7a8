import React, { useState } from "react";
import { ManualPick, ExpertConfidence } from "../../types/manualPick";
import { submitManualPick } from "../../utils/api";

interface ManualPickFormProps {
  adminPassword: string;
}

const ManualPickForm: React.FC<ManualPickFormProps> = ({ adminPassword }) => {
  const [form, setForm] = useState<ManualPick>({
    name: "",
    odds: 1.0,
    league: [],
    pick_origin: [],
    reusable: true,
    capital_limit: 0,
    mutual_exclusion: -1,
    pick_type: "MoneyLine",
    player_team: "",
    stat_type: "MoneyLine",
    prediction: 1,
    player_name: "",
    stat_threshold: undefined,
  });

  const [statusMsg, setStatusMsg] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const availableLeagues: string[] = ["NBA", "NFL", "MLB", "NHL"];

  const handleInputChange = (
    field: keyof ManualPick,
    value: string | number | boolean | string[] | undefined
  ): void => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleLeagueToggle = (league: string): void => {
    setForm((prev) => ({
      ...prev,
      league: prev.league.includes(league)
        ? prev.league.filter((l) => l !== league)
        : [...prev.league, league],
    }));
  };

  const handleExpertChange = (
    index: number,
    field: keyof ExpertConfidence,
    value: string | number
  ): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: prev.pick_origin.map((expert, i) =>
        i === index ? { ...expert, [field]: value } : expert
      ),
    }));
  };

  const addExpert = (): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: [...prev.pick_origin, { name: "", confidence: 75 }],
    }));
  };

  const removeExpert = (index: number): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: prev.pick_origin.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ): Promise<void> => {
    e.preventDefault();

    if (
      !form.name ||
      form.odds <= 0 ||
      form.league.length === 0 ||
      form.pick_origin.length === 0
    ) {
      setStatusMsg("Please fill all required fields");
      return;
    }

    if (
      form.pick_type === "StatOverUnder" &&
      (!form.player_name || form.stat_threshold === undefined)
    ) {
      setStatusMsg(
        "Player name and stat threshold are required for Stat Over/Under picks"
      );
      return;
    }

    setIsSubmitting(true);
    setStatusMsg("");

    try {
      // Prepare data aligned with events table schema
      const submissionData = {
        name: form.name,
        odds: form.odds,
        league: form.league,
        pick_origin: form.pick_origin,
        reusable: form.reusable,
        capital_limit: form.capital_limit,
        mutual_exclusion: form.mutual_exclusion,
        pick_type: form.pick_type,
        player_team: form.player_team || "None",
        stat_type: form.stat_type || "MoneyLine",
        prediction: form.prediction,
        player_name: form.player_name || "",
        stat_threshold: form.stat_threshold,
        admin_password: adminPassword,
      };

      const response = await submitManualPick(submissionData);
      if (response.success) {
        setStatusMsg("Pick submitted successfully!");
        setForm({
          name: "",
          odds: 1.0,
          league: [],
          pick_origin: [],
          reusable: true,
          capital_limit: 0,
          mutual_exclusion: -1,
          pick_type: "MoneyLine",
          player_team: "",
          stat_type: "MoneyLine",
          prediction: 1,
          player_name: "",
          stat_threshold: undefined,
        });
      } else {
        setStatusMsg(`Error: ${response.response}`);
      }
    } catch (error) {
      setStatusMsg(`Network error: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gray-900 bg-opacity-80 border border-[#58C612] rounded-xl shadow-lg text-white">
      <h2 className="text-2xl font-bold mb-6 text-center text-[#58C612]">
        Manual Pick Form
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Pick Name *
          </label>
          <input
            type="text"
            value={form.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            placeholder="e.g., Lakers vs Warriors"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Decimal Odds *
          </label>
          <input
            type="number"
            step="0.01"
            min="1.01"
            value={form.odds}
            onChange={(e) =>
              handleInputChange("odds", parseFloat(e.target.value))
            }
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Leagues *
          </label>
          <div className="flex flex-wrap gap-2">
            {availableLeagues.map((league) => (
              <button
                key={league}
                type="button"
                onClick={() => handleLeagueToggle(league)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors cursor-pointer ${
                  form.league.includes(league)
                    ? "bg-[#58C612] text-black"
                    : "bg-[#233e6c] text-white hover:bg-[#1a2d54]"
                }`}
              >
                {league}
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Pick Type
          </label>
          <select
            value={form.pick_type}
            onChange={(e) =>
              handleInputChange(
                "pick_type",
                e.target.value as "MoneyLine" | "StatOverUnder"
              )
            }
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
          >
            <option value="MoneyLine">MoneyLine</option>
            <option value="StatOverUnder">Stat Over/Under</option>
          </select>
        </div>

        {form.pick_type === "StatOverUnder" && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Player Name *
              </label>
              <input
                type="text"
                value={form.player_name || ""}
                onChange={(e) =>
                  handleInputChange("player_name", e.target.value)
                }
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., LeBron James"
                required={form.pick_type === "StatOverUnder"}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Player/Team
              </label>
              <input
                type="text"
                value={form.player_team}
                onChange={(e) =>
                  handleInputChange("player_team", e.target.value)
                }
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., LAL, MIA"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Stat Type
              </label>
              <input
                type="text"
                value={form.stat_type}
                onChange={(e) => handleInputChange("stat_type", e.target.value)}
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., Points, Rebounds, Assists"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Stat Threshold *
              </label>
              <input
                type="number"
                step="0.5"
                min="0"
                value={form.stat_threshold || ""}
                onChange={(e) =>
                  handleInputChange(
                    "stat_threshold",
                    e.target.value ? parseFloat(e.target.value) : undefined
                  )
                }
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., 25.5"
                required={form.pick_type === "StatOverUnder"}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Prediction
              </label>
              <select
                value={form.prediction}
                onChange={(e) =>
                  handleInputChange(
                    "prediction",
                    parseInt(e.target.value) as 1 | 0
                  )
                }
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              >
                <option value={1}>Over/Higher</option>
                <option value={0}>Under/Lower</option>
              </select>
            </div>
          </>
        )}

        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-300">
              Expert Predictions *
            </label>
            <button
              type="button"
              onClick={addExpert}
              className="px-3 py-1 bg-[#58C612] text-black text-sm rounded-md hover:bg-[#449e10] transition-colors cursor-pointer"
            >
              Add Expert
            </button>
          </div>

          {form.pick_origin.map((expert, index) => (
            <div key={index} className="flex gap-2 mb-2 items-center">
              <input
                type="text"
                value={expert.name}
                onChange={(e) =>
                  handleExpertChange(index, "name", e.target.value)
                }
                className="flex-1 bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="Expert Name"
                required
              />
              <input
                type="number"
                min="0"
                max="100"
                value={expert.confidence}
                onChange={(e) =>
                  handleExpertChange(
                    index,
                    "confidence",
                    parseInt(e.target.value)
                  )
                }
                className="w-20 bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="75"
                required
              />
              <button
                type="button"
                onClick={() => removeExpert(index)}
                className="px-2 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-500 transition-colors cursor-pointer"
              >
                Remove
              </button>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Capital Limit
            </label>
            <input
              type="number"
              min="0"
              value={form.capital_limit}
              onChange={(e) =>
                handleInputChange("capital_limit", parseInt(e.target.value))
              }
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Mutual Exclusion
            </label>
            <input
              type="number"
              value={form.mutual_exclusion}
              onChange={(e) =>
                handleInputChange("mutual_exclusion", parseInt(e.target.value))
              }
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>

          <div className="flex items-center">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={form.reusable}
                onChange={(e) =>
                  handleInputChange("reusable", e.target.checked)
                }
                className="mr-2 h-4 w-4 text-[#58C612] focus:ring-[#58C612] border-gray-300 rounded"
              />
              <span className="text-sm font-medium text-gray-300">
                Reusable
              </span>
            </label>
          </div>
        </div>

        <div className="flex justify-between items-center pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-[#58C612] hover:bg-[#449e10] text-black font-bold rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 cursor-pointer"
          >
            {isSubmitting ? "Submitting..." : "Submit Pick"}
          </button>

          {statusMsg && (
            <div
              className={`text-sm ${
                statusMsg.includes("Error") || statusMsg.includes("error")
                  ? "text-red-400"
                  : "text-green-400"
              }`}
            >
              {statusMsg}
            </div>
          )}
        </div>
      </form>
    </div>
  );
};

export default ManualPickForm;
