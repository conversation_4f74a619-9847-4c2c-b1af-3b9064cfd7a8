import React, { useState } from "react";
import ManualPickForm from "../../components/ManualPickForm";
import Logo from "../../components/Logo";

const PASSWORD = "ppadmin42";

const AdminManualPick: React.FC = () => {
  const [password, setPassword] = useState("");
  const [authenticated, setAuthenticated] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    if (password === PASSWORD) {
      setAuthenticated(true);
    } else {
      setPassword("");
    }
  };

  if (!authenticated) {
    return (
      <div className="min-h-screen bg-[#061844] flex flex-col items-center justify-center p-4 select-none text-white">
        <Logo />
        <div className="w-full max-w-md bg-gray-900 bg-opacity-80 border border-[#58C612] rounded-xl shadow-xl p-8">
          <h1 className="text-2xl font-bold text-center text-[#58C612] mb-6">
            Admin Access
          </h1>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-[#58C612] mb-2">
                Admin Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter admin password"
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-[#58C612]"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full py-2 bg-[#58C612] hover:bg-[#449e10] text-black font-bold rounded-md transition-colors duration-200"
            >
              Enter
            </button>
            {password !== "" && password !== PASSWORD && (
              <p className="text-red-500 text-sm text-center">
                Incorrect password
              </p>
            )}
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#061844] py-8 select-none text-white">
      <Logo />
      <ManualPickForm adminPassword={PASSWORD} />
    </div>
  );
};

export default AdminManualPick;
